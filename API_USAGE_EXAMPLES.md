# 用户认证API使用示例

## 概述

本文档提供了用户注册、登录和认证相关API的使用示例。

## 1. 用户注册

### 请求
```http
POST /user/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456"
}
```

### 响应
```json
{
  "success": true,
  "message": "成功",
  "data": {
    "userId": 1234567890,
    "username": "testuser",
    "message": "用户注册成功"
  }
}
```

### 验证规则
- 用户名：3-20个字符，只能包含字母、数字和下划线
- 密码：6-20个字符
- 用户名必须唯一

## 2. 用户登录

### 请求
```http
POST /user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456"
}
```

### 响应
```json
{
  "success": true,
  "message": "成功",
  "data": {
    "userId": 1234567890,
    "username": "testuser",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwidXNlcm5hbWUiOiJ0ZXN0dXNlciIsImlhdCI6MTYzOTU2NzgwMCwiZXhwIjoxNjQwMTcyNjAwfQ.signature",
    "tokenType": "Bearer"
  }
}
```

## 3. 获取用户信息（需要认证）

### 请求
```http
GET /user/info
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应
```json
{
  "success": true,
  "message": "成功",
  "data": {
    "userId": 1234567890,
    "username": "testuser",
    "createTime": null,
    "lastLoginTime": "2025-06-18T09:50:00"
  }
}
```

## 4. 联盟相关API（需要认证）

### 创建联盟
```http
POST /sanBing/coalition
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "areaCode": 1,
  "name": "测试联盟"
}
```

### 添加联盟成员
```http
POST /sanBing/member
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "coalitionCode": "ABC123",
  "name": "张三",
  "level": 50,
  "combatPower": 100000,
  "addition": 1.5,
  "gatheringCapacity": 200
}
```

## 5. 错误响应示例

### 用户名已存在
```json
{
  "success": false,
  "message": "用户名已存在",
  "data": null
}
```

### 用户名或密码错误
```json
{
  "success": false,
  "message": "用户名或密码错误",
  "data": null
}
```

### Token无效或过期
```json
{
  "success": false,
  "message": "Token无效或已过期"
}
```

### 参数验证失败
```json
{
  "success": false,
  "message": "用户名长度必须在3-20个字符之间",
  "data": null
}
```

## 6. 使用流程

1. **注册新用户**：调用 `/user/register` 接口
2. **用户登录**：调用 `/user/login` 接口获取JWT Token
3. **访问受保护的API**：在请求头中携带 `Authorization: Bearer <token>`
4. **Token过期处理**：当收到401错误时，重新登录获取新Token

## 7. 安全注意事项

- 密码使用BCrypt算法加密存储
- JWT Token默认7天过期
- 所有联盟相关操作都需要认证
- Token应该安全存储，避免泄露
- 建议在HTTPS环境下使用

## 8. 数据库初始化

运行以下SQL脚本创建用户表：

```sql
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(20) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    creator BIGINT,
    gmt_create TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    gmt_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updater BIGINT,
    deleted INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_deleted ON users(deleted);
```

## 9. 测试账号

系统预置了以下测试账号（密码都是 "123456"）：
- admin
- testuser

可以直接使用这些账号进行登录测试。
