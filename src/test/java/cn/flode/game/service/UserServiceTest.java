package cn.flode.game.service;

import cn.flode.game.controller.dto.UserLoginDTO;
import cn.flode.game.controller.dto.UserRegisterDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.controller.vo.RegisterResult;
import cn.flode.game.entity.User;
import cn.flode.game.mapper.UserMapper;
import cn.flode.game.util.JwtUtils;
import com.mybatisflex.core.query.QueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 用户服务测试类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtUtils jwtUtils;

    @InjectMocks
    private UserService userService;

    private UserRegisterDTO registerDTO;
    private UserLoginDTO loginDTO;
    private User user;

    @BeforeEach
    void setUp() {
        registerDTO = new UserRegisterDTO();
        registerDTO.setUsername("testuser");
        registerDTO.setPassword("123456");

        loginDTO = new UserLoginDTO();
        loginDTO.setUsername("testuser");
        loginDTO.setPassword("123456");

        user = User.builder()
                .id(1L)
                .username("testuser")
                .password("$2a$10$encodedPassword")
                .build();
    }

    @Test
    void testRegisterSuccess() {
        // Given
        when(userMapper.selectCountByQuery(any(QueryWrapper.class))).thenReturn(0L);
        when(passwordEncoder.encode("123456")).thenReturn("$2a$10$encodedPassword");
        when(userMapper.insert(any(User.class))).thenReturn(1);

        // When
        RegisterResult result = userService.register(registerDTO);

        // Then
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertEquals("用户注册成功", result.getMessage());
        verify(passwordEncoder).encode("123456");
        verify(userMapper).insert(any(User.class));
    }

    @Test
    void testRegisterUsernameExists() {
        // Given
        when(userMapper.selectCountByQuery(any(QueryWrapper.class))).thenReturn(1L);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.register(registerDTO);
        });
        assertEquals("用户名已存在", exception.getMessage());
        verify(passwordEncoder, never()).encode(anyString());
        verify(userMapper, never()).insert(any(User.class));
    }

    @Test
    void testLoginSuccess() {
        // Given
        when(userMapper.selectOneByQuery(any(QueryWrapper.class))).thenReturn(user);
        when(passwordEncoder.matches("123456", "$2a$10$encodedPassword")).thenReturn(true);
        when(jwtUtils.generateToken(1L, "testuser")).thenReturn("mock-jwt-token");

        // When
        LoginResult result = userService.login(loginDTO);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getUserId());
        assertEquals("testuser", result.getUsername());
        assertEquals("mock-jwt-token", result.getToken());
        assertEquals("Bearer", result.getTokenType());
    }

    @Test
    void testLoginUserNotFound() {
        // Given
        when(userMapper.selectOneByQuery(any(QueryWrapper.class))).thenReturn(null);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.login(loginDTO);
        });
        assertEquals("用户名或密码错误", exception.getMessage());
        verify(passwordEncoder, never()).matches(anyString(), anyString());
        verify(jwtUtils, never()).generateToken(anyLong(), anyString());
    }

    @Test
    void testLoginPasswordMismatch() {
        // Given
        when(userMapper.selectOneByQuery(any(QueryWrapper.class))).thenReturn(user);
        when(passwordEncoder.matches("123456", "$2a$10$encodedPassword")).thenReturn(false);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.login(loginDTO);
        });
        assertEquals("用户名或密码错误", exception.getMessage());
        verify(jwtUtils, never()).generateToken(anyLong(), anyString());
    }

    @Test
    void testCheckUsernameExists() {
        // Given
        when(userMapper.selectCountByQuery(any(QueryWrapper.class))).thenReturn(1L);

        // When
        boolean exists = userService.checkUsernameExists("testuser");

        // Then
        assertTrue(exists);
    }

    @Test
    void testCheckUsernameNotExists() {
        // Given
        when(userMapper.selectCountByQuery(any(QueryWrapper.class))).thenReturn(0L);

        // When
        boolean exists = userService.checkUsernameExists("testuser");

        // Then
        assertFalse(exists);
    }

    @Test
    void testGetUserByUsername() {
        // Given
        when(userMapper.selectOneByQuery(any(QueryWrapper.class))).thenReturn(user);

        // When
        User result = userService.getUserByUsername("testuser");

        // Then
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertEquals(1L, result.getId());
    }
}
