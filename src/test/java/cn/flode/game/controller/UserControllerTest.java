package cn.flode.game.controller;

import cn.flode.game.controller.dto.UserLoginDTO;
import cn.flode.game.controller.dto.UserRegisterDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.controller.vo.RegisterResult;
import cn.flode.game.service.UserService;
import cn.flode.game.util.JsonUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 用户控制器测试
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@WebMvcTest(UserController.class)
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Test
    void testRegisterSuccess() throws Exception {
        // Given
        UserRegisterDTO registerDTO = new UserRegisterDTO();
        registerDTO.setUsername("testuser");
        registerDTO.setPassword("123456");

        RegisterResult registerResult = RegisterResult.success(1L, "testuser");
        when(userService.register(any(UserRegisterDTO.class))).thenReturn(registerResult);

        // When & Then
        mockMvc.perform(post("/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JsonUtils.toString(registerDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.userId").value(1))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.message").value("用户注册成功"));
    }

    @Test
    void testRegisterValidationError() throws Exception {
        // Given - 无效的注册数据
        UserRegisterDTO registerDTO = new UserRegisterDTO();
        registerDTO.setUsername("ab"); // 用户名太短
        registerDTO.setPassword("123"); // 密码太短

        // When & Then
        mockMvc.perform(post("/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JsonUtils.toString(registerDTO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testLoginSuccess() throws Exception {
        // Given
        UserLoginDTO loginDTO = new UserLoginDTO();
        loginDTO.setUsername("testuser");
        loginDTO.setPassword("123456");

        LoginResult loginResult = LoginResult.success(1L, "testuser", "mock-jwt-token");
        when(userService.login(any(UserLoginDTO.class))).thenReturn(loginResult);

        // When & Then
        mockMvc.perform(post("/user/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JsonUtils.toString(loginDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.userId").value(1))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.token").value("mock-jwt-token"))
                .andExpect(jsonPath("$.data.tokenType").value("Bearer"));
    }

    @Test
    void testLoginValidationError() throws Exception {
        // Given - 无效的登录数据
        UserLoginDTO loginDTO = new UserLoginDTO();
        loginDTO.setUsername(""); // 用户名为空
        loginDTO.setPassword(""); // 密码为空

        // When & Then
        mockMvc.perform(post("/user/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JsonUtils.toString(loginDTO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetUserInfoWithoutToken() throws Exception {
        // When & Then - 没有Token应该被拦截器拦截
        mockMvc.perform(get("/user/info"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testGetUserInfoWithInvalidToken() throws Exception {
        // When & Then - 无效Token应该被拦截器拦截
        mockMvc.perform(get("/user/info")
                        .header("Authorization", "Bearer invalid-token"))
                .andExpect(status().isUnauthorized());
    }
}
