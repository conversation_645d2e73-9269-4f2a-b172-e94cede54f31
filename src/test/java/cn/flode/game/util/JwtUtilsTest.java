package cn.flode.game.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT工具类测试
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
class JwtUtilsTest {

    private JwtUtils jwtUtils;

    @BeforeEach
    void setUp() {
        jwtUtils = new JwtUtils();
        // 设置测试用的密钥和过期时间
        ReflectionTestUtils.setField(jwtUtils, "secret", "test-secret-key-for-jwt-token-generation-and-validation-must-be-at-least-256-bits");
        ReflectionTestUtils.setField(jwtUtils, "expiration", Duration.ofHours(1));
    }

    @Test
    void testGenerateToken() {
        // Given
        Long userId = 123L;
        String username = "testuser";

        // When
        String token = jwtUtils.generateToken(userId, username);

        // Then
        assertNotNull(token);
        assertFalse(token.isEmpty());
        assertTrue(token.contains("."));  // JWT格式包含点分隔符
    }

    @Test
    void testGetUserIdFromToken() {
        // Given
        Long userId = 123L;
        String username = "testuser";
        String token = jwtUtils.generateToken(userId, username);

        // When
        Long extractedUserId = jwtUtils.getUserIdFromToken(token);

        // Then
        assertEquals(userId, extractedUserId);
    }

    @Test
    void testGetUsernameFromToken() {
        // Given
        Long userId = 123L;
        String username = "testuser";
        String token = jwtUtils.generateToken(userId, username);

        // When
        String extractedUsername = jwtUtils.getUsernameFromToken(token);

        // Then
        assertEquals(username, extractedUsername);
    }

    @Test
    void testValidateTokenValid() {
        // Given
        Long userId = 123L;
        String username = "testuser";
        String token = jwtUtils.generateToken(userId, username);

        // When
        boolean isValid = jwtUtils.validateToken(token);

        // Then
        assertTrue(isValid);
    }

    @Test
    void testValidateTokenInvalid() {
        // Given
        String invalidToken = "invalid.token.here";

        // When
        boolean isValid = jwtUtils.validateToken(invalidToken);

        // Then
        assertFalse(isValid);
    }

    @Test
    void testValidateTokenEmpty() {
        // Given
        String emptyToken = "";

        // When
        boolean isValid = jwtUtils.validateToken(emptyToken);

        // Then
        assertFalse(isValid);
    }

    @Test
    void testValidateTokenNull() {
        // Given
        String nullToken = null;

        // When
        boolean isValid = jwtUtils.validateToken(nullToken);

        // Then
        assertFalse(isValid);
    }

    @Test
    void testTokenExpiration() throws InterruptedException {
        // Given - 设置很短的过期时间
        ReflectionTestUtils.setField(jwtUtils, "expiration", Duration.ofMillis(100));
        Long userId = 123L;
        String username = "testuser";
        String token = jwtUtils.generateToken(userId, username);

        // 等待Token过期
        Thread.sleep(200);

        // When
        boolean isValid = jwtUtils.validateToken(token);

        // Then
        assertFalse(isValid);
    }
}
