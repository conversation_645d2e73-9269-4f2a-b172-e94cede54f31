-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(20) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    creator BIGIN<PERSON>,
    gmt_create TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    gmt_update TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updater BIGINT,
    deleted INTEGER DEFAULT 0
);

-- 创建用户名索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- 创建软删除索引
CREATE INDEX IF NOT EXISTS idx_users_deleted ON users(deleted);
