package cn.flode.game.controller.sanbing;

import cn.flode.game.controller.sanbing.dto.CoalitionCreateDTO;
import cn.flode.game.controller.sanbing.dto.MemberAddDTO;
import cn.flode.game.controller.sanbing.vo.CoalitionKey;
import cn.flode.game.controller.sanbing.vo.MemberAddResult;
import cn.flode.game.service.SanBingCoalitionService;
import cn.flode.game.service.SanBingMemberService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/sanBing")
class SanBingController {
  // 管理员
  // 创建联盟
  // 创建分组
  // 添加联盟成员
  // 分组添加成员
  // 分组移除成员

  private final SanBingCoalitionService coalitionService;
  private final SanBingMemberService memberService;

  /** 创建联盟 */
  @PostMapping("/coalition")
  public CoalitionKey coalition(@Validated @RequestBody CoalitionCreateDTO create) {
    return coalitionService.create(create.getAreaCode(), create.getName());
  }

  /**
   * 添加联盟成员
   */
  @PostMapping("/member")
  public MemberAddResult addMember(@Validated @RequestBody MemberAddDTO memberAddDTO) {
    Long memberId = memberService.addMember(memberAddDTO);
    return MemberAddResult.success(memberId);
  }

  /**
   * 更新联盟成员信息
   */
  @PutMapping("/member")
  public String updateMember() {
    return "updateMember";
  }

  /**
   * 移除联盟成员
   */
  @DeleteMapping("/member")
  public String removeMember() {
    return "deleteMember";
  }

  @PostMapping("/group")
  public String createGroup() {
    return "group";
  }

  @PutMapping("/group")
  public String updateGroup() {
    return "updateGroup";
  }

  @DeleteMapping("/group")
  public String deleteGroup() {
    return "deleteGroup";
  }
}
