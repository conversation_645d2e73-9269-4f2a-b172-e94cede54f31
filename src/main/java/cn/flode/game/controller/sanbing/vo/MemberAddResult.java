package cn.flode.game.controller.sanbing.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberAddResult {

    private Long memberId;
    
    private String message;
    
    public static MemberAddResult success(Long memberId) {
        return new MemberAddResult(memberId, "成员添加成功，状态为待审核");
    }
}
