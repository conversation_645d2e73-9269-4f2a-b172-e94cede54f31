package cn.flode.game.controller.sanbing.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MemberAddDTO {

    @NotEmpty(message = "联盟代码不能为空")
    private String coalitionCode;

    @NotEmpty(message = "成员姓名不能为空")
    private String name;

    @Min(value = 1, message = "等级必须大于0")
    @NotNull(message = "等级不能为空")
    private Integer level;

    @Min(value = 0, message = "战力不能为负数")
    @NotNull(message = "战力不能为空")
    private Integer combatPower;

    @Min(value = 0, message = "加成不能为负数")
    private Double addition;

    @Min(value = 0, message = "集结容量不能为负数")
    private Integer gatheringCapacity;
}
