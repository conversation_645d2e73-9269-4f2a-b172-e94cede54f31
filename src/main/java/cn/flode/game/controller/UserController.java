package cn.flode.game.controller;

import cn.flode.game.controller.dto.UserLoginDTO;
import cn.flode.game.controller.dto.UserRegisterDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.controller.vo.RegisterResult;
import cn.flode.game.controller.vo.UserInfo;
import cn.flode.game.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserController {

    private final UserService userService;

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public RegisterResult register(@Validated @RequestBody UserRegisterDTO registerDTO) {
        return userService.register(registerDTO);
    }

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public LoginResult login(@Validated @RequestBody UserLoginDTO loginDTO) {
        return userService.login(loginDTO);
    }

    /**
     * 获取当前用户信息
     *
     * @param request HTTP请求对象
     * @return 用户信息
     */
    @GetMapping("/info")
    public UserInfo getUserInfo(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        String username = (String) request.getAttribute("username");
        return UserInfo.of(userId, username);
    }

    /**
     * 获取用户详细信息（别名接口）
     *
     * @param request HTTP请求对象
     * @return 用户信息
     */
    @GetMapping("/profile")
    public UserInfo getUserProfile(HttpServletRequest request) {
        return getUserInfo(request);
    }
}
