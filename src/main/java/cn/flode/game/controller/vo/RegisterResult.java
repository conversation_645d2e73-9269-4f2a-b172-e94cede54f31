package cn.flode.game.controller.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 注册结果VO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegisterResult {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 消息
     */
    private String message;

    public static RegisterResult success(Long userId, String username) {
        return new RegisterResult(userId, username, "用户注册成功");
    }
}
