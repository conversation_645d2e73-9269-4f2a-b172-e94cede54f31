package cn.flode.game.controller.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户信息VO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 创建简单用户信息
     *
     * @param userId 用户ID
     * @param username 用户名
     * @return 用户信息
     */
    public static UserInfo of(Long userId, String username) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        userInfo.setUsername(username);
        userInfo.setLastLoginTime(LocalDateTime.now());
        return userInfo;
    }
}
