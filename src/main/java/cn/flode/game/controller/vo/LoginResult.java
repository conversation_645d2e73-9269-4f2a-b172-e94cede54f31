package cn.flode.game.controller.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录结果VO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginResult {

  /** 用户ID */
  private Long userId;

  /** 用户名 */
  private String username;

  /** JWT Token */
  private String token;

  /** Token类型 */
  private String tokenType = "Bearer";

  public static LoginResult success(Long userId, String username, String token) {
    return new LoginResult(userId, username, token, "Bearer");
  }
}
