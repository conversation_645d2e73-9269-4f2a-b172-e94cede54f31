package cn.flode.game.entity;

import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.framework.BaseEntity;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;

import java.io.Serial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("san_bing_member")
public class SanBingMember extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long coalitionId;

    private String name;

    private ApproveStatus status;

    private Integer level;

    private Integer combatPower;

    private Double addition;

    private Integer gatheringCapacity;

}
