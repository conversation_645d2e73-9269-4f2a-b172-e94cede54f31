package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.MemberAddDTO;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingMember;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.mapper.SanBingMemberMapper;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingMemberService extends ServiceImpl<SanBingMemberMapper, SanBingMember> {

    private final SanBingCoalitionService coalitionService;

    /**
     * 添加联盟成员
     * @param memberAddDTO 成员信息
     * @return 成员ID
     */
    public Long addMember(MemberAddDTO memberAddDTO) {
        // 1. 根据联盟code查找联盟
        SanBingCoalition coalition = coalitionService.getByCode(memberAddDTO.getCoalitionCode());
        if (coalition == null) {
            throw new RuntimeException("联盟不存在");
        }

        // 2. 检查审核通过的成员数量是否超过100
        long approvedMemberCount = getApprovedMemberCount(coalition.getId());
        if (approvedMemberCount >= 100) {
            throw new RuntimeException("联盟成员数量已达上限(100人)");
        }

        // 检查成员名称是否已存在
        boolean nameExists = checkMemberNameExists(coalition.getId(), memberAddDTO.getName());
        if (nameExists) {
            throw new RuntimeException("该联盟中已存在同名成员");
        }

        // 3. 创建成员对象
        SanBingMember member = SanBingMember.builder()
                .coalitionId(coalition.getId())
                .name(memberAddDTO.getName())
                .level(memberAddDTO.getLevel())
                .combatPower(memberAddDTO.getCombatPower())
                .addition(memberAddDTO.getAddition())
                .gatheringCapacity(memberAddDTO.getGatheringCapacity())
                .status(ApproveStatus.TO_BE_APPROVED) // 默认待审核状态
                .build();

        // 4. 保存成员
        save(member);
        return member.getId();
    }

    /**
     * 获取联盟中审核通过的成员数量
     * @param coalitionId 联盟ID
     * @return 审核通过的成员数量
     */
    public long getApprovedMemberCount(Long coalitionId) {
        return mapper.selectCountByQuery(
                QueryWrapper.create()
                        .eq(SanBingMember::getCoalitionId, coalitionId)
                        .eq(SanBingMember::getStatus, ApproveStatus.APPROVED)
        );
    }

    /**
     * 检查联盟中是否已存在同名成员
     * @param coalitionId 联盟ID
     * @param name 成员姓名
     * @return 是否存在同名成员
     */
    public boolean checkMemberNameExists(Long coalitionId, String name) {
        long count = mapper.selectCountByQuery(
                QueryWrapper.create()
                        .eq(SanBingMember::getCoalitionId, coalitionId)
                        .eq(SanBingMember::getName, name)
        );
        return count > 0;
    }
}
