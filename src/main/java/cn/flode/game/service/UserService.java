package cn.flode.game.service;

import cn.flode.game.controller.dto.UserLoginDTO;
import cn.flode.game.controller.dto.UserRegisterDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.controller.vo.RegisterResult;
import cn.flode.game.entity.User;
import cn.flode.game.mapper.UserMapper;
import cn.flode.game.util.JwtUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 用户服务层实现。
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
@RequiredArgsConstructor
public class UserService extends ServiceImpl<UserMapper, User> {

    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    public RegisterResult register(UserRegisterDTO registerDTO) {
        // 1. 检查用户名是否已存在
        boolean usernameExists = checkUsernameExists(registerDTO.getUsername());
        if (usernameExists) {
            throw new RuntimeException("用户名已存在");
        }

        // 2. 加密密码
        String encodedPassword = passwordEncoder.encode(registerDTO.getPassword());

        // 3. 创建用户对象
        User user = User.builder()
                .username(registerDTO.getUsername())
                .password(encodedPassword)
                .build();

        // 4. 保存用户
        save(user);

        return RegisterResult.success(user.getId(), user.getUsername());
    }

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    public LoginResult login(UserLoginDTO loginDTO) {
        // 1. 根据用户名查找用户
        User user = getUserByUsername(loginDTO.getUsername());
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 2. 验证密码
        boolean passwordMatches = passwordEncoder.matches(loginDTO.getPassword(), user.getPassword());
        if (!passwordMatches) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 3. 生成JWT Token
        String token = jwtUtils.generateToken(user.getId(), user.getUsername());

        return LoginResult.success(user.getId(), user.getUsername(), token);
    }

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    public boolean checkUsernameExists(String username) {
        long count = mapper.selectCountByQuery(
                QueryWrapper.create().eq(User::getUsername, username)
        );
        return count > 0;
    }

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户对象，如果不存在返回null
     */
    public User getUserByUsername(String username) {
        return mapper.selectOneByQuery(
                QueryWrapper.create().eq(User::getUsername, username)
        );
    }
}
