package cn.flode.game.config.mybatis.listener;

import cn.flode.framework.UserContext;
import cn.flode.framework.mybatis.BaseEntity;
import com.mybatisflex.annotation.AbstractUpdateListener;
import java.time.LocalDateTime;

public class UpdateListener extends AbstractUpdateListener<BaseEntity> {
  @Override
  public void doUpdate(BaseEntity baseEntity) {
    baseEntity.setUpdater(UserContext.getUserIdOpt().orElse(null));
    baseEntity.setGmtUpdate(LocalDateTime.now());
  }
}
