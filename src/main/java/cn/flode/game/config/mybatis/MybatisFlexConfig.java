package cn.flode.game.config.mybatis;

import cn.flode.game.config.mybatis.listener.InsertListener;
import cn.flode.game.config.mybatis.listener.UpdateListener;
import cn.flode.game.framework.BaseEntity;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.AuditMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MybatisFlexConfig implements InitializingBean {

  @Override
  public void afterPropertiesSet() {
    InsertListener insertListener = new InsertListener();
    UpdateListener updateListener = new UpdateListener();
    FlexGlobalConfig config = FlexGlobalConfig.getDefaultConfig();
    config.registerInsertListener(insertListener, BaseEntity.class);
    config.registerUpdateListener(updateListener, BaseEntity.class);

    AuditManager.setAuditEnable(true);
    AuditManager.setMessageFactory(
        () -> {
          AuditMessage message = new AuditMessage();
          message.setUser(SecurityUtils.getCurrentUserId());
          return message;
        });
    AuditManager.setMessageCollector(
        auditMessage ->
            log.info(
                "user {} execSql {} spend {}ms",
                auditMessage.getUser(),
                auditMessage.getFullSql(),
                auditMessage.getElapsedTime()));
  }
}
