package cn.flode.game.config;

import cn.flode.game.interceptor.JwtInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final JwtInterceptor jwtInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                // 拦截需要认证的路径
                .addPathPatterns("/sanBing/**", "/user/profile", "/user/info")
                // 排除不需要认证的路径
                .excludePathPatterns(
                        "/user/register",
                        "/user/login",
                        "/error",
                        "/favicon.ico"
                );
    }
}
